#!/usr/bin/env python3
"""
YOLO11x 猪检测推理脚本
使用训练好的模型进行猪检测推理
"""

from ultralytics import YOLO
import os
from pathlib import Path
import argparse
import torch

# 检查GPU可用性
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"使用设备: {device}")

def find_best_model():
    """查找最佳训练模型"""
    # 可能的模型路径
    model_paths = [
        "runs/weights/best.pt",
        "runs/weights/last.pt",
        "yolo11x.pt"  # 备用预训练模型
    ]
    
    for model_path in model_paths:
        if Path(model_path).exists():
            return model_path
    
    return None

def detect_images(model_path, source_path, output_dir="runs/detect"):
    """
    对图片进行猪检测
    
    Args:
        model_path: 模型文件路径
        source_path: 输入图片/目录路径
        output_dir: 输出目录
    """
    print(f"=== YOLO11x 猪检测推理 ===")
    print(f"模型: {model_path}")
    print(f"输入: {source_path}")
    print(f"输出: {output_dir}")
    
    # 加载模型
    try:
        model = YOLO(model_path)
        print("模型加载成功")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return False
    
    # 推理参数配置
    predict_args = {
        'source': source_path,         # 输入源
        'project': output_dir,         # 输出项目目录
        'name': 'pig_detection',       # 实验名称
        'save': True,                  # 保存结果图片
        'save_txt': True,              # 保存检测结果文本
        'save_conf': True,             # 保存置信度
        'conf': 0.25,                  # 置信度阈值
        'iou': 0.45,                   # NMS IoU阈值
        'imgsz': 640,                  # 输入图像尺寸
        'device': device,              # 自动选择设备
        'verbose': True,               # 详细输出
    }
    
    print("\n推理配置:")
    for key, value in predict_args.items():
        print(f"  {key}: {value}")
    
    try:
        # 开始推理
        print(f"\n开始推理...")
        results = model.predict(**predict_args)
        
        print(f"\n推理完成!")
        print(f"结果保存在: {output_dir}/pig_detection/")
        
        return True
        
    except Exception as e:
        print(f"推理过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YOLO11x 猪检测推理")
    parser.add_argument("--source", "-s", default="8/", 
                       help="输入图片/目录路径 (默认: pig_dataset/images/val)")
    parser.add_argument("--model", "-m", default="runs/train/pig_detection/weights/best.pt",
                       help="模型文件路径 (默认: 自动查找最佳模型)")
    parser.add_argument("--output", "-o", default="runs/detect",
                       help="输出目录 (默认: runs/detect)")
    
    args = parser.parse_args()
    
    # 确定模型路径
    if args.model:
        model_path = args.model
        if not Path(model_path).exists():
            print(f"错误: 指定的模型文件不存在: {model_path}")
            return
    else:
        model_path = find_best_model()
        if not model_path:
            print("错误: 未找到可用的模型文件")
            print("请确保:")
            print("1. 已完成模型训练 (python train.py)")
            print("2. 或者指定模型路径 (--model path/to/model.pt)")
            return
    
    # 检查输入源
    if not Path(args.source).exists():
        print(f"错误: 输入源不存在: {args.source}")
        print("请确保:")
        print("1. 已运行 prepare_dataset.py 生成数据集")
        print("2. 或者指定正确的输入路径")
        return
    
    # 开始推理
    success = detect_images(model_path, args.source, args.output)
    
    if success:
        print("\n=== 推理完成 ===")
        print("您可以:")
        print(f"1. 查看检测结果图片: {args.output}/pig_detection/")
        print(f"2. 查看检测结果文本: {args.output}/pig_detection/labels/")
        print("3. 使用不同的输入源: python detect.py --source path/to/images")
    else:
        print("\n推理失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()
